"""
Data models for Mai Voice Agent
Defines request/response models and data structures
"""

from typing import Optional, List, Dict, Any, Literal
from pydantic import BaseModel, Field, EmailStr
from datetime import datetime
from enum import Enum

class SessionType(str, Enum):
    """Types of communication sessions"""
    TEXT = "text"
    VOICE = "voice"
    VIDEO = "video"

class SessionStatus(str, Enum):
    """Session status states"""
    CONNECTING = "connecting"
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    ERROR = "error"

class MessageType(str, Enum):
    """Types of messages in conversation"""
    USER = "user"
    MAI = "mai"
    SYSTEM = "system"

# Request Models
class ChatRequest(BaseModel):
    """Request model for text chat"""
    prompt: str = Field(..., min_length=1, max_length=2000, description="User message")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="AI response creativity")
    max_tokens: Optional[int] = Field(512, ge=1, le=2048, description="Maximum response length")
    session_id: Optional[str] = Field(None, description="Session identifier")

class VoiceRequest(BaseModel):
    """Request model for voice chat setup"""
    webrtc_id: str = Field(..., description="WebRTC connection identifier")
    voice_name: str = Field("Aoede", description="Voice to use for responses")
    mode: str = Field("audio", description="Communication mode")
    session_id: Optional[str] = Field(None, description="Session identifier")

class VideoRequest(BaseModel):
    """Request model for video chat setup"""
    webrtc_id: str = Field(..., description="WebRTC connection identifier")
    voice_name: str = Field("Aoede", description="Voice to use for responses")
    enable_video: bool = Field(True, description="Enable video stream")
    session_id: Optional[str] = Field(None, description="Session identifier")

class ContactInfo(BaseModel):
    """Contact information model"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Full name")
    email: Optional[EmailStr] = Field(None, description="Email address")
    phone: Optional[str] = Field(None, min_length=5, max_length=20, description="Phone number")
    company: Optional[str] = Field(None, min_length=1, max_length=100, description="Company name")
    purpose: Optional[str] = Field(None, min_length=1, max_length=1000, description="Purpose of inquiry")

class SessionEndRequest(BaseModel):
    """Request to end a session"""
    session_id: Optional[str] = Field(None, description="Session to end")
    reason: Optional[str] = Field("user_request", description="Reason for ending session")

# Response Models
class ChatResponse(BaseModel):
    """Response model for text chat"""
    response: str = Field(..., description="AI response message")
    session_id: Optional[str] = Field(None, description="Session identifier")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")

class StatusResponse(BaseModel):
    """General status response"""
    status: str = Field(..., description="Status message")
    message: Optional[str] = Field(None, description="Additional information")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Status timestamp")

class SessionResponse(BaseModel):
    """Session operation response"""
    status: str = Field(..., description="Operation status")
    session_id: Optional[str] = Field(None, description="Session identifier")
    session_type: Optional[SessionType] = Field(None, description="Type of session")
    message: Optional[str] = Field(None, description="Status message")

class VoiceStatusResponse(BaseModel):
    """Voice chat status response"""
    available: bool = Field(..., description="Whether voice chat is available")
    voice_name: str = Field("Aoede", description="Current voice setting")
    webrtc_available: bool = Field(..., description="WebRTC availability")
    error: Optional[str] = Field(None, description="Error message if any")

class HealthResponse(BaseModel):
    """Health check response"""
    status: str = Field(..., description="Service health status")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Check timestamp")
    service: str = Field("mai-voice-agent", description="Service name")
    version: str = Field("1.0.0", description="Service version")
    features: Dict[str, bool] = Field(default_factory=dict, description="Available features")
    active_sessions: int = Field(0, description="Number of active sessions")

class EmailResponse(BaseModel):
    """Email operation response"""
    status: str = Field(..., description="Email operation status")
    message: str = Field(..., description="Result message")
    emails_sent: int = Field(0, description="Number of emails sent")

# Internal Data Models
class ConversationMessage(BaseModel):
    """Individual conversation message"""
    type: MessageType = Field(..., description="Message type")
    content: str = Field(..., description="Message content")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Message timestamp")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")

class ConversationMemory(BaseModel):
    """Conversation memory and context"""
    session_id: str = Field(..., description="Session identifier")
    messages: List[ConversationMessage] = Field(default_factory=list, description="Conversation messages")
    contact_info: ContactInfo = Field(default_factory=ContactInfo, description="Extracted contact information")
    summary: Optional[str] = Field(None, description="Conversation summary")
    started_at: datetime = Field(default_factory=datetime.utcnow, description="Session start time")
    last_activity: datetime = Field(default_factory=datetime.utcnow, description="Last activity time")
    session_type: SessionType = Field(SessionType.TEXT, description="Type of session")

    def add_message(self, message_type: MessageType, content: str, metadata: Optional[Dict[str, Any]] = None):
        """Add a message to the conversation"""
        message = ConversationMessage(
            type=message_type,
            content=content,
            metadata=metadata or {}
        )
        self.messages.append(message)
        self.last_activity = datetime.utcnow()

    def get_conversation_text(self) -> str:
        """Get conversation as formatted text"""
        lines = []
        for msg in self.messages:
            speaker = "User" if msg.type == MessageType.USER else "Mai"
            lines.append(f"{speaker}: {msg.content}")
        return "\n".join(lines)

class SessionManager:
    """Manages active sessions"""

    def __init__(self):
        self.sessions: Dict[str, ConversationMemory] = {}

    def create_session(self, session_id: str, session_type: SessionType) -> ConversationMemory:
        """Create a new session"""
        memory = ConversationMemory(
            session_id=session_id,
            session_type=session_type
        )
        self.sessions[session_id] = memory
        return memory

    def get_session(self, session_id: str) -> Optional[ConversationMemory]:
        """Get session by ID"""
        return self.sessions.get(session_id)

    def end_session(self, session_id: str) -> bool:
        """End and remove session"""
        if session_id in self.sessions:
            del self.sessions[session_id]
            return True
        return False

    def get_active_sessions(self) -> List[str]:
        """Get list of active session IDs"""
        return list(self.sessions.keys())

    def cleanup_old_sessions(self, max_age_minutes: int = 30):
        """Remove sessions older than specified age"""
        from datetime import timedelta
        cutoff = datetime.utcnow() - timedelta(minutes=max_age_minutes)

        expired_sessions = [
            session_id for session_id, memory in self.sessions.items()
            if memory.last_activity < cutoff
        ]

        for session_id in expired_sessions:
            del self.sessions[session_id]

        return len(expired_sessions)

# Global session manager instance
session_manager = SessionManager()


