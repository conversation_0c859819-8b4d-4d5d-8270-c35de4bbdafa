/* Mai Voice Agent - Enhanced Styles */

:root {
    /* Color Palette */
    --color-primary: #6366f1;
    --color-secondary: #8b5cf6;
    --color-accent: #06b6d4;
    --color-background: #0f172a;
    --color-surface: #1e293b;
    --color-surface-light: #334155;
    --color-text: #e2e8f0;
    --color-text-muted: #94a3b8;
    --color-text-dark: #64748b;
    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-error: #ef4444;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--color-primary), var(--color-secondary), var(--color-accent));
    --gradient-background: linear-gradient(135deg, var(--color-background) 0%, #1e1b4b 50%, #312e81 100%);
    --gradient-surface: linear-gradient(145deg, rgba(30, 41, 59, 0.95), rgba(51, 65, 85, 0.8));
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
    
    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: var(--gradient-background);
    color: var(--color-text);
    min-height: 100vh;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Animated Background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-20px) translateY(-10px); }
    50% { transform: translateX(20px) translateY(10px); }
    75% { transform: translateX(-10px) translateY(20px); }
}

/* Main Layout */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-md);
    position: relative;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: var(--space-2xl);
    padding: var(--space-xl);
    animation: fadeInUp 0.8s ease-out;
}

.mai-avatar {
    width: 140px;
    height: 140px;
    border-radius: var(--radius-full);
    margin: 0 auto var(--space-lg);
    border: 4px solid transparent;
    background: var(--gradient-primary);
    padding: 4px;
    overflow: hidden;
    box-shadow: var(--shadow-glow), var(--shadow-xl);
    position: relative;
    animation: avatarPulse 3s ease-in-out infinite;
}

.mai-avatar::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, var(--color-primary), transparent);
    animation: rotate 4s linear infinite;
    z-index: -1;
}

.mai-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--radius-full);
    background: var(--color-surface);
}

@keyframes avatarPulse {
    0%, 100% { transform: scale(1); box-shadow: var(--shadow-glow), var(--shadow-xl); }
    50% { transform: scale(1.05); box-shadow: 0 0 30px rgba(99, 102, 241, 0.5), var(--shadow-2xl); }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.header h1 {
    font-size: 3.5rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    margin-bottom: var(--space-sm);
    letter-spacing: -0.02em;
    text-shadow: 0 0 30px rgba(99, 102, 241, 0.3);
}

.header .subtitle {
    color: var(--color-text-muted);
    font-size: 1.25rem;
    font-weight: 500;
    margin-bottom: var(--space-lg);
    opacity: 0.9;
}

.disclaimer {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    margin-bottom: var(--space-lg);
    font-size: 0.95rem;
    line-height: 1.6;
    max-width: 600px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
}

/* Container Styles */
.container {
    width: 100%;
    max-width: 1200px;
    background: var(--gradient-surface);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    padding: var(--space-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-2xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

/* Mode Selector */
.mode-selector {
    display: flex;
    justify-content: center;
    margin-bottom: var(--space-2xl);
    background: rgba(15, 23, 42, 0.6);
    border-radius: var(--radius-xl);
    padding: var(--space-sm);
     -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.mode-btn {
    flex: 1;
    padding: var(--space-md) var(--space-lg);
    border: none;
    background: transparent;
    color: var(--color-text);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    font-size: 1rem;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.mode-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left var(--transition-normal);
    z-index: -1;
}

.mode-btn.active {
    color: white;
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.mode-btn.active::before {
    left: 0;
}

.mode-btn:hover:not(.active) {
    background: rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

/* Chat Interface */
.chat-interface {
    display: none;
    flex-direction: column;
    height: 600px;
    animation: fadeIn var(--transition-normal);
}

.chat-interface.active {
    display: flex;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-lg);
    background: rgba(15, 23, 42, 0.4);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
     -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    scrollbar-width: thin;
    scrollbar-color: var(--color-primary) transparent;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--color-primary);
    border-radius: var(--radius-full);
}

.message {
    margin-bottom: var(--space-lg);
    padding: var(--space-md) var(--space-lg);
    border-radius: var(--radius-lg);
    max-width: 85%;
    word-wrap: break-word;
    position: relative;
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.message.user {
    background: var(--gradient-primary);
    color: white;
    margin-left: auto;
    text-align: right;
    box-shadow: var(--shadow-md);
}

.message.mai {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    color: var(--color-text);
     -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

/* Input Styles */
.chat-input-container {
    display: flex;
    gap: var(--space-md);
    align-items: flex-end;
}

.chat-input {
    flex: 1;
    padding: var(--space-md) var(--space-lg);
    background: rgba(15, 23, 42, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    color: var(--color-text);
    font-size: 1rem;
    transition: all var(--transition-normal);
     -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    resize: vertical;
    min-height: 50px;
    max-height: 120px;
}

.chat-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    background: rgba(15, 23, 42, 0.8);
}

.send-btn {
    padding: var(--space-md) var(--space-xl);
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-weight: 600;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
    min-height: 50px;
}

.send-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.send-btn:active {
    transform: translateY(0);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from { 
        opacity: 0; 
        transform: translateY(30px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        width: 95%;
        padding: var(--space-lg);
        margin: var(--space-md);
    }
    
    .header h1 {
        font-size: 2.5rem;
    }
    
    .mai-avatar {
        width: 100px;
        height: 100px;
    }
    
    .mode-selector {
        flex-direction: column;
        gap: var(--space-sm);
    }
    
    .chat-interface {
        height: 500px;
    }
    
    .chat-input-container {
        flex-direction: column;
    }
    
    .send-btn {
        width: 100%;
    }
}

/* Voice Interface */
.voice-interface {
    display: none;
    text-align: center;
    padding: var(--space-2xl);
    animation: fadeIn var(--transition-normal);
}

.voice-interface.active {
    display: block;
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md) var(--space-xl);
    border-radius: var(--radius-full);
    font-size: 1rem;
    font-weight: 600;
    margin: var(--space-lg) 0;
     -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.status-indicator.connected {
    background: rgba(16, 185, 129, 0.15);
    color: var(--color-success);
    border: 1px solid rgba(16, 185, 129, 0.4);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.2);
}

.status-indicator.disconnected {
    background: rgba(239, 68, 68, 0.15);
    color: var(--color-error);
    border: 1px solid rgba(239, 68, 68, 0.4);
}

.status-indicator.connecting {
    background: rgba(245, 158, 11, 0.15);
    color: var(--color-warning);
    border: 1px solid rgba(245, 158, 11, 0.4);
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: var(--radius-full);
    background: currentColor;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.1); }
}

.voice-controls {
    display: flex;
    justify-content: center;
    gap: var(--space-lg);
    margin: var(--space-2xl) 0;
    flex-wrap: wrap;
}

.voice-btn {
    padding: var(--space-lg) var(--space-2xl);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all var(--transition-normal);
    min-width: 160px;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.voice-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    transition: all 0.6s ease;
    transform: translate(-50%, -50%);
}

.voice-btn:hover::before {
    width: 300px;
    height: 300px;
}

.voice-btn.start {
    background: linear-gradient(135deg, var(--color-success), #059669);
    color: white;
}

.voice-btn.stop {
    background: linear-gradient(135deg, var(--color-error), #dc2626);
    color: white;
}

.voice-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.voice-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.voice-btn:disabled:hover {
    transform: none;
    box-shadow: var(--shadow-lg);
}

/* Video Interface */
.video-interface {
    display: none;
    text-align: center;
    padding: var(--space-2xl);
    animation: fadeIn var(--transition-normal);
}

.video-interface.active {
    display: block;
}

.video-container {
    display: flex;
    gap: var(--space-lg);
    justify-content: center;
    margin: var(--space-2xl) 0;
    flex-wrap: wrap;
}

.video-element {
    width: 300px;
    height: 225px;
    background: rgba(15, 23, 42, 0.8);
    border-radius: var(--radius-lg);
    border: 2px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
    position: relative;
    box-shadow: var(--shadow-lg);
}

.video-element video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-element.local {
    border-color: var(--color-primary);
}

.video-element.remote {
    border-color: var(--color-success);
}

/* Mai Container in Video Chat */
.mai-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
    position: relative;
    overflow: hidden;
}

.mai-container .mai-avatar {
    width: 200px;
    height: 200px;
    margin: var(--space-lg) auto;
    position: relative;
    z-index: 2;
}

.mai-gif {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--radius-full);
    background: var(--color-surface);
}

/* Wave Animation */
.wave-animation {
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: end;
    gap: 3px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 3;
}

.wave-animation.active {
    opacity: 1;
}

.wave-bar {
    width: 4px;
    height: 8px;
    background: linear-gradient(to top, var(--color-primary), var(--color-secondary));
    border-radius: 2px;
    animation: waveBar 1.2s ease-in-out infinite;
}

.wave-bar:nth-child(1) { animation-delay: 0s; }
.wave-bar:nth-child(2) { animation-delay: 0.1s; }
.wave-bar:nth-child(3) { animation-delay: 0.2s; }
.wave-bar:nth-child(4) { animation-delay: 0.3s; }
.wave-bar:nth-child(5) { animation-delay: 0.4s; }
.wave-bar:nth-child(6) { animation-delay: 0.3s; }
.wave-bar:nth-child(7) { animation-delay: 0.2s; }
.wave-bar:nth-child(8) { animation-delay: 0.1s; }

@keyframes waveBar {
    0%, 100% {
        height: 8px;
        background: linear-gradient(to top, var(--color-primary), var(--color-secondary));
    }
    50% {
        height: 24px;
        background: linear-gradient(to top, var(--color-secondary), var(--color-accent));
    }
}

.video-label {
    position: absolute;
    top: var(--space-sm);
    left: var(--space-sm);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
}

/* File Upload Panel */
.file-upload-panel {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: var(--space-xl);
    margin-top: var(--space-lg);
    background: rgba(15, 23, 42, 0.4);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.file-upload-panel.active {
    display: flex;
}

.upload-zone {
    border: 2px dashed rgba(99, 102, 241, 0.3);
    border-radius: var(--radius-lg);
    padding: var(--space-xl);
    background: rgba(99, 102, 241, 0.05);
    cursor: pointer;
    transition: all var(--transition-normal);
    width: 100%;
    min-height: 150px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.upload-zone:hover {
    border-color: var(--color-primary);
    background: rgba(99, 102, 241, 0.1);
}

.upload-zone.dragover {
    border-color: var(--color-primary);
    background: rgba(99, 102, 241, 0.15);
}

.upload-icon {
    font-size: 2rem;
    margin-bottom: var(--space-md);
    opacity: 0.6;
}

.upload-text {
    font-size: 1rem;
    margin-bottom: var(--space-sm);
    color: var(--color-text);
}

.upload-subtext {
    font-size: 0.8rem;
    opacity: 0.7;
    color: var(--color-text-secondary);
}

.file-input {
    display: none;
}

.uploaded-files {
    margin-top: var(--space-md);
    max-height: 100px;
    overflow-y: auto;
    width: 100%;
}

.uploaded-file {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: var(--radius-md);
    padding: var(--space-sm) var(--space-md);
    margin: var(--space-sm) 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
}

.file-remove {
    background: none;
    border: none;
    color: var(--color-error);
    cursor: pointer;
    margin-left: auto;
    padding: var(--space-xs);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-remove:hover {
    background: rgba(239, 68, 68, 0.1);
}

/* Contact Form */
.contact-form {
    display: none;
    background: rgba(15, 23, 42, 0.4);
    padding: var(--space-2xl);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: var(--space-2xl);
     -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    animation: fadeIn var(--transition-normal);
}

.contact-form.active {
    display: block;
}

.contact-form h3 {
    margin-bottom: var(--space-xl);
    color: var(--color-text);
    font-size: 1.5rem;
    font-weight: 700;
    text-align: center;
}

.form-group {
    margin-bottom: var(--space-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--space-sm);
    color: var(--color-text);
    font-weight: 600;
    font-size: 0.95rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--space-md) var(--space-lg);
    background: rgba(15, 23, 42, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    color: var(--color-text);
    font-size: 1rem;
    transition: all var(--transition-normal);
     -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    background: rgba(15, 23, 42, 0.8);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.submit-btn {
    width: 100%;
    padding: var(--space-lg);
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    transition: all 0.6s ease;
    transform: translate(-50%, -50%);
}

.submit-btn:hover::before {
    width: 400px;
    height: 400px;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

/* Footer */
.footer {
    text-align: center;
    margin-top: var(--space-2xl);
    padding: var(--space-xl);
    color: var(--color-text-dark);
    font-size: 0.9rem;
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.footer a {
    color: var(--color-primary);
    text-decoration: none;
    font-weight: 600;
    transition: color var(--transition-normal);
}

.footer a:hover {
    color: var(--color-secondary);
    text-decoration: underline;
}

/* Loading States */
.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.hidden { display: none !important; }
.visible { display: block !important; }
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
