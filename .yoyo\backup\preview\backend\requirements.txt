# Core FastAPI and server dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Google Gemini AI SDK
google-genai>=0.8.0
google-generativeai>=0.3.2

# WebRTC and real-time communication - WORKING COMPATIBLE VERSIONS
aiortc==1.4.0
fastrtc==0.0.24

# Audio and Video processing with specific compatible versions
numpy==1.26.4
scipy==1.11.4
av==10.0.0

# Image processing for video support
Pillow>=10.0.0,<11.0.0

# Configuration and environment management
python-dotenv>=1.0.0,<2.0.0
pydantic>=2.4.0,<3.0.0
pydantic-settings>=2.0.0,<3.0.0
email-validator>=2.0.0,<3.0.0

# HTTP client and async support
httpx>=0.28.1,<1.0.0
aiofiles>=23.0.0,<24.0.0

# CORS and middleware
python-multipart>=0.0.6

# WebSocket support
websockets>=13.0,<15.0

# Production server optimization
gunicorn>=21.2.0,<22.0.0

# Cloud deployment helpers
psutil>=5.9.0

# Email functionality is built into Python (smtplib)
# No additional dependencies needed for email
