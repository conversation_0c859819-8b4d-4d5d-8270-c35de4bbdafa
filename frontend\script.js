/**
 * Mai Voice Agent Frontend JavaScript
 * Clean implementation matching the organized backend structure
 */

// Global application state
const AppState = {
    currentMode: 'chat',
    sessionId: null,
    isConnected: false,
    websocket: null,
    httpFallbackMode: false,

    // Voice/Video state
    isRecording: false,
    isVideoActive: false,
    mediaRecorder: null,
    localStream: null,

    // Conversation memory
    conversationMemory: [],
    contactInfo: {}
};

// Configuration - Auto-detects environment
const CONFIG = (() => {
    const isProduction = window.location.hostname.includes('railway.app') ||
                        window.location.hostname.includes('up.railway.app');
    const isLocalhost = window.location.hostname === 'localhost' ||
                       window.location.hostname === '127.0.0.1';

    return {
        apiBaseUrl: '/api',  // Always use relative URLs since we're serving from same domain
        websocketUrl: `${window.location.protocol === 'https:' ? 'wss:' : 'ws'}://${window.location.host}/api/ws`,
        isProduction,
        isLocalhost,
        baseUrl: `${window.location.protocol}//${window.location.host}`
    };
})();

// Utility functions
const Utils = {
    generateSessionId() {
        return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    },
    
    formatTimestamp(date = new Date()) {
        return date.toLocaleTimeString();
    },
    
    sanitizeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },
    
    showNotification(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`);
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '15px 20px',
            borderRadius: '8px',
            color: 'white',
            fontWeight: '600',
            zIndex: '10000',
            maxWidth: '300px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });
        
        const colors = {
            info: '#6366f1',
            success: '#10b981',
            warning: '#f59e0b',
            error: '#ef4444'
        };
        notification.style.backgroundColor = colors[type] || colors.info;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    },
    
    async apiCall(endpoint, options = {}) {
        try {
            const response = await fetch(`${CONFIG.apiBaseUrl}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`API call failed: ${response.status} ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API call error:', error);
            Utils.showNotification(`API Error: ${error.message}`, 'error');
            throw error;
        }
    },

    async testConnection() {
        try {
            console.log('Testing API connection...');
            const response = await this.apiCall('/health');
            console.log('Health check successful:', response);
            Utils.showNotification('✅ Connected to Mai successfully!', 'success');
            return true;
        } catch (error) {
            console.error('Connection test failed:', error);
            Utils.showNotification('❌ Failed to connect to Mai. Please check your connection.', 'error');
            return false;
        }
    }
};

// Mode management
const ModeManager = {
    currentMode: 'text',

    init() {
        this.setupModeButtons();
        this.showMode('text'); // Start with text mode
    },

    setupModeButtons() {
        const textBtn = document.getElementById('textModeBtn');
        const voiceBtn = document.getElementById('voiceModeBtn');
        const videoBtn = document.getElementById('videoModeBtn');
        const contactBtn = document.getElementById('contactModeBtn');

        if (textBtn) {
            textBtn.addEventListener('click', () => this.switchMode('text'));
        }
        if (voiceBtn) {
            voiceBtn.addEventListener('click', () => this.switchMode('voice'));
        }
        if (videoBtn) {
            videoBtn.addEventListener('click', () => this.switchMode('video'));
        }
        if (contactBtn) {
            contactBtn.addEventListener('click', () => this.switchMode('contact'));
        }
    },

    async switchMode(mode) {
        if (this.currentMode === mode) return;

        // Clean up current mode
        await this.cleanupCurrentMode();

        // Switch to new mode
        this.currentMode = mode;
        this.showMode(mode);
        
        console.log(`Switched to ${mode} mode`);
    },

    async cleanupCurrentMode() {
        try {
            if (AppState.isRecording) {
                await VoiceChat.stop();
            }
            if (AppState.isVideoActive) {
                await VideoChat.stop();
            }
            if (AppState.websocket) {
                // Keep text chat connection open
            }
        } catch (error) {
            console.error('Error cleaning up mode:', error);
        }
    },

    showMode(mode) {
        // Hide all mode containers
        const containers = ['textChatContainer', 'voiceChatContainer', 'videoChatContainer', 'contactChatContainer'];
        containers.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.style.display = 'none';
            }
        });

        // Show selected mode container
        const targetContainer = document.getElementById(`${mode}ChatContainer`);
        if (targetContainer) {
            targetContainer.style.display = 'block';
        }

        // Update button states
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        const activeBtn = document.getElementById(`${mode}ModeBtn`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }

        // Initialize mode-specific functionality
        if (mode === 'voice' && !VoiceChat.initialized) {
            VoiceChat.init();
            VoiceChat.initialized = true;
        } else if (mode === 'video' && !VideoChat.initialized) {
            VideoChat.init();
            VideoChat.initialized = true;
        } else if (mode === 'contact' && !ContactForm.initialized) {
            ContactForm.init();
            ContactForm.initialized = true;
        }
    }
};

// Text Chat functionality
const TextChat = {
    init() {
        this.setupEventListeners();
        this.connect();
    },

    setupEventListeners() {
        const sendBtn = document.getElementById('sendBtn');
        const chatInput = document.getElementById('chatInput');

        if (sendBtn) {
            sendBtn.addEventListener('click', () => this.sendMessage());
        }

        if (chatInput) {
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }
    },

    connect() {
        if (!AppState.sessionId) {
            AppState.sessionId = Utils.generateSessionId();
        }

        console.log('🔌 Initializing simple HTTP-based text chat...');

        // Skip WebSocket entirely and use HTTP mode directly
        this.initHttpMode();
    },

    async initHttpMode() {
        try {
            console.log('🔄 Testing API connection...');
            const response = await fetch(`${CONFIG.apiBaseUrl}/health`);

            if (response.ok) {
                console.log('✅ API is reachable, using HTTP mode for text chat');
                AppState.isConnected = true;
                AppState.httpFallbackMode = true;
                AppState.websocket = null;
                this.updateConnectionStatus('connected');
                Utils.showNotification('✅ Connected to Mai chat service!', 'success');
            } else {
                throw new Error(`API health check failed: ${response.status}`);
            }
        } catch (error) {
            console.error('❌ HTTP connection failed:', error);
            AppState.isConnected = false;
            this.updateConnectionStatus('disconnected');
            Utils.showNotification('Unable to connect to chat service', 'error');
        }
    },

    async tryHttpFallback() {
        console.log('🔄 Attempting HTTP fallback mode...');
        AppState.isConnected = false;
        AppState.websocket = null;

        // Test if the API is reachable
        try {
            const response = await fetch(`${CONFIG.apiBaseUrl}/health`);
            if (response.ok) {
                console.log('✅ API is reachable, using HTTP fallback mode');
                AppState.isConnected = true;
                AppState.httpFallbackMode = true;
                this.updateConnectionStatus('connected');
                Utils.showNotification('⚠️ Connected in fallback mode (HTTP)', 'warning');
            } else {
                throw new Error(`API health check failed: ${response.status}`);
            }
        } catch (error) {
            console.error('❌ HTTP fallback also failed:', error);
            Utils.showNotification('Unable to connect to chat service', 'error');
            this.updateConnectionStatus('disconnected');
        }
    },

    disconnect() {
        if (AppState.websocket) {
            AppState.websocket.close();
            AppState.websocket = null;
        }
        AppState.httpFallbackMode = false;
    },

    sendMessage() {
        const chatInput = document.getElementById('chatInput');
        const message = chatInput.value.trim();

        if (!message) return;

        console.log('🔍 Checking connection status:', {
            isConnected: AppState.isConnected,
            websocket: AppState.websocket,
            httpFallbackMode: AppState.httpFallbackMode,
            readyState: AppState.websocket?.readyState
        });

        if (!AppState.isConnected) {
            console.log('❌ Cannot send message - not connected');
            Utils.showNotification('Not connected to chat service', 'error');
            return;
        }

        // Add user message to UI
        this.addMessage('user', message);

        // Store in conversation memory
        AppState.conversationMemory.push({
            type: 'user',
            content: message,
            timestamp: new Date()
        });

        // Clear input
        chatInput.value = '';

        // Show typing indicator
        this.showTypingIndicator();

        // Always use HTTP for text chat (simple and reliable)
        this.sendMessageHTTP(message);
    },

    async sendMessageHTTP(message) {
        try {
            console.log('📤 Sending HTTP message:', message);
            const response = await fetch(`${CONFIG.apiBaseUrl}/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt: message,
                    session_id: AppState.sessionId
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('📨 Received HTTP response:', data);

            this.hideTypingIndicator();

            if (data.response) {
                this.addMessage('mai', data.response);
                AppState.conversationMemory.push({
                    type: 'mai',
                    content: data.response,
                    timestamp: new Date()
                });
            } else {
                throw new Error('No response in API reply');
            }

        } catch (error) {
            console.error('❌ HTTP message failed:', error);
            this.hideTypingIndicator();
            this.addMessage('mai', 'Sorry, I encountered an error processing your message. Please try again.');
            Utils.showNotification('Message failed to send', 'error');
        }
    },

    handleMessage(data) {
        this.hideTypingIndicator();

        switch (data.type) {
            case 'chat_response':
                this.addMessage('mai', data.message);
                AppState.conversationMemory.push({
                    type: 'mai',
                    content: data.message,
                    timestamp: new Date()
                });
                break;

            case 'session_ended':
                Utils.showNotification('Session ended. Follow-up emails sent.', 'success');
                break;

            case 'error':
                Utils.showNotification(data.message || 'An error occurred', 'error');
                break;

            case 'pong':
                // Handle ping/pong for connection keep-alive
                break;
        }
    },

    addMessage(sender, content) {
        const messagesContainer = document.getElementById('chatMessages');
        if (!messagesContainer) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        messageDiv.textContent = Utils.sanitizeHtml(content);

        messagesContainer.appendChild(messageDiv);

        // Smooth scroll to bottom with a slight delay to ensure DOM is updated
        setTimeout(() => {
            messagesContainer.scrollTo({
                top: messagesContainer.scrollHeight,
                behavior: 'smooth'
            });
        }, 50);

        // Add animation
        messageDiv.style.opacity = '0';
        messageDiv.style.transform = 'translateY(20px)';
        setTimeout(() => {
            messageDiv.style.transition = 'all 0.3s ease';
            messageDiv.style.opacity = '1';
            messageDiv.style.transform = 'translateY(0)';
        }, 10);
    },

    showTypingIndicator() {
        const messagesContainer = document.getElementById('chatMessages');
        if (!messagesContainer) return;

        // Remove existing typing indicator
        this.hideTypingIndicator();

        const typingDiv = document.createElement('div');
        typingDiv.className = 'message mai typing-indicator';
        typingDiv.id = 'typingIndicator';
        typingDiv.textContent = 'Mai is typing...';

        messagesContainer.appendChild(typingDiv);

        // Smooth scroll to bottom
        setTimeout(() => {
            messagesContainer.scrollTo({
                top: messagesContainer.scrollHeight,
                behavior: 'smooth'
            });
        }, 50);
    },

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    },

    updateConnectionStatus(status) {
        const statusElements = document.querySelectorAll('.status-indicator');
        statusElements.forEach(el => {
            el.className = `status-indicator ${status}`;
            const statusText = el.querySelector('span:last-child');
            if (statusText) {
                statusText.textContent = status === 'connected' ? 'Ready' : 'Disconnected';
            }
        });
    }
};

// Voice Chat functionality with real-time WebRTC
const VoiceChat = {
    websocket: null,
    audioContext: null,
    mediaRecorder: null,
    recordedChunks: [],

    init() {
        this.setupEventListeners();
        this.checkMicrophonePermission();
    },

    setupEventListeners() {
        const startBtn = document.getElementById('startVoiceBtn');
        const stopBtn = document.getElementById('stopVoiceBtn');

        if (startBtn) {
            startBtn.addEventListener('click', () => this.start());
        }

        if (stopBtn) {
            stopBtn.addEventListener('click', () => this.stop());
        }
    },

    async checkMicrophonePermission() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            stream.getTracks().forEach(track => track.stop());
            this.updateStatus('ready', 'Microphone ready');
        } catch (error) {
            console.error('Microphone permission denied:', error);
            this.updateStatus('error', 'Microphone access denied');
            Utils.showNotification('Please allow microphone access for voice chat', 'warning');
        }
    },

    async start() {
        try {
            if (!AppState.sessionId) {
                AppState.sessionId = Utils.generateSessionId();
            }

            this.updateStatus('connecting', 'Connecting to Mai...');

            // Set input parameters for FastRTC stream
            const inputResponse = await Utils.apiCall('/input_hook', {
                method: 'POST',
                body: JSON.stringify({
                    webrtc_id: AppState.sessionId,
                    voice_name: 'Aoede',
                    mode: 'audio'
                })
            });

            if (inputResponse.status === 'error') {
                throw new Error(inputResponse.message || 'Failed to initialize voice session');
            }

            // Initialize WebRTC peer connection
            this.peerConnection = new RTCPeerConnection({
                iceServers: [
                    { urls: 'stun:stun.l.google.com:19302' },
                    { urls: 'stun:stun1.l.google.com:19302' }
                ]
            });

            // Get microphone access
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 16000
                }
            });

            AppState.localStream = stream;
            AppState.isRecording = true;

            // Add audio track to peer connection
            stream.getTracks().forEach(track => this.peerConnection.addTrack(track, stream));

            // Setup audio context for visualization
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)({ sampleRate: 24000 });

            // Handle incoming audio stream from Mai
            this.peerConnection.addEventListener('track', (evt) => {
                if (evt.track.kind === 'audio') {
                    const audioOutput = this.createAudioElement();
                    if (audioOutput.srcObject !== evt.streams[0]) {
                        audioOutput.srcObject = evt.streams[0];
                        audioOutput.play().catch(e => console.error("Audio play failed:", e));

                        // Show wave animation when Mai speaks
                        this.showWaveAnimation();

                        // Hide animation when audio ends
                        audioOutput.onended = () => this.hideWaveAnimation();
                    }
                }
            });

            // Handle ICE candidates
            this.peerConnection.onicecandidate = ({ candidate }) => {
                if (candidate) {
                    console.debug("Sending ICE candidate", candidate);
                    fetch('/audio/webrtc/offer', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            candidate: candidate.toJSON(),
                            webrtc_id: AppState.sessionId,
                            type: "ice-candidate",
                        })
                    }).catch(e => console.error("Error sending ICE candidate:", e));
                }
            };

            // Create data channel for control messages
            const dataChannel = this.peerConnection.createDataChannel('control');
            dataChannel.onmessage = (event) => {
                const eventJson = JSON.parse(event.data);
                if (eventJson.type === "error") {
                    Utils.showNotification(eventJson.message, 'error');
                } else if (eventJson.type === "send_input") {
                    fetch(`${CONFIG.apiBaseUrl}/input_hook`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            webrtc_id: AppState.sessionId,
                            voice_name: 'Aoede',
                            mode: "audio"
                        })
                    });
                }
            };

            // Create and send WebRTC offer
            const offer = await this.peerConnection.createOffer();
            await this.peerConnection.setLocalDescription(offer);

            const response = await fetch('/audio/webrtc/offer', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    sdp: this.peerConnection.localDescription.sdp,
                    type: this.peerConnection.localDescription.type,
                    webrtc_id: AppState.sessionId,
                })
            });

            const serverResponse = await response.json();

            if (serverResponse.status === 'failed') {
                const errorMsg = serverResponse.meta.error === 'concurrency_limit_reached'
                    ? `Too many active sessions. Maximum limit is ${serverResponse.meta.limit}. Please try again later.`
                    : serverResponse.meta.message || 'WebRTC connection failed';
                throw new Error(errorMsg);
            }

            if (serverResponse.sdp) {
                await this.peerConnection.setRemoteDescription(new RTCSessionDescription({
                    type: serverResponse.type,
                    sdp: serverResponse.sdp
                }));
            }

            this.updateButtons(true);
            this.updateStatus('connected', 'Voice chat active - speak to Mai!');
            Utils.showNotification('Voice chat started! Speak to Mai', 'success');

        } catch (error) {
            console.error('Failed to start voice chat:', error);
            Utils.showNotification('Failed to start voice chat: ' + error.message, 'error');
            this.updateStatus('error', 'Failed to start voice chat');
        }
    },

    async connectVoiceWebSocket() {
        const wsUrl = `${CONFIG.websocketUrl.replace('/api/ws', '/api/voice/ws')}/${AppState.sessionId}`;
        this.websocket = new WebSocket(wsUrl);

        this.websocket.onopen = () => {
            console.log('Voice WebSocket connected');
        };

        this.websocket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleVoiceMessage(data);
        };

        this.websocket.onclose = () => {
            console.log('Voice WebSocket disconnected');
        };

        this.websocket.onerror = (error) => {
            console.error('Voice WebSocket error:', error);
        };

        // Wait for connection
        return new Promise((resolve, reject) => {
            this.websocket.onopen = () => {
                console.log('Voice WebSocket connected');
                resolve();
            };
            this.websocket.onerror = reject;
        });
    },

    async processAudioChunk(audioBlob) {
        try {
            // Convert audio blob to base64
            const arrayBuffer = await audioBlob.arrayBuffer();
            const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

            // Send audio data to backend
            if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                this.websocket.send(JSON.stringify({
                    type: 'audio_data',
                    audio_data: base64Audio,
                    session_id: AppState.sessionId
                }));
            }
        } catch (error) {
            console.error('Error processing audio chunk:', error);
        }
    },

    handleVoiceMessage(data) {
        switch (data.type) {
            case 'voice_ready':
                Utils.showNotification(`Mai is ready to talk! (${data.voice_name})`, 'success');
                break;

            case 'voice_response':
                // Show wave animation when Mai starts speaking
                this.showWaveAnimation();

                // Play audio response from Mai
                this.playAudioResponse(data.audio_data);

                // Show text response
                if (data.text) {
                    Utils.showNotification(`Mai: ${data.text}`, 'info');
                }
                break;

            case 'voice_ended':
                Utils.showNotification('Voice session ended', 'info');
                this.stop();
                break;

            case 'error':
                Utils.showNotification(`Voice error: ${data.message}`, 'error');
                break;
        }
    },

    async playAudioResponse(base64Audio) {
        try {
            // Convert base64 to audio blob
            const binaryString = atob(base64Audio);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            const audioBlob = new Blob([bytes], { type: 'audio/wav' });
            const audioUrl = URL.createObjectURL(audioBlob);

            // Play audio
            const audio = new Audio(audioUrl);
            audio.play();

            // Clean up URL after playing
            audio.onended = () => {
                URL.revokeObjectURL(audioUrl);
            };

        } catch (error) {
            console.error('Error playing audio response:', error);
            Utils.showNotification('Error playing Mai\'s voice response', 'error');
        }
    },

    async setupWebRTCConnection(offer) {
        try {
            // Create RTCPeerConnection
            this.peerConnection = new RTCPeerConnection({
                iceServers: [
                    { urls: 'stun:stun.l.google.com:19302' },
                    { urls: 'stun:stun1.l.google.com:19302' }
                ]
            });

            // Handle incoming audio stream
            this.peerConnection.ontrack = (event) => {
                const [remoteStream] = event.streams;
                this.playRemoteAudio(remoteStream);
            };

            // Handle ICE candidates
            this.peerConnection.onicecandidate = (event) => {
                if (event.candidate) {
                    // Send ICE candidate to server if needed
                    console.log('ICE candidate:', event.candidate);
                }
            };

            // Set remote description (offer from server)
            await this.peerConnection.setRemoteDescription(new RTCSessionDescription(offer));

            // Get user media and add to peer connection
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 16000
                }
            });

            stream.getTracks().forEach(track => {
                this.peerConnection.addTrack(track, stream);
            });

            // Create answer
            const answer = await this.peerConnection.createAnswer();
            await this.peerConnection.setLocalDescription(answer);

            // Send answer to server
            await Utils.apiCall(`/webrtc/answer/${AppState.sessionId}`, {
                method: 'POST',
                body: JSON.stringify({ sdp: answer })
            });

            this.updateStatus('connected', 'Real-time voice chat active');
            Utils.showNotification('Real-time voice chat connected!', 'success');

        } catch (error) {
            console.error('WebRTC setup error:', error);
            this.updateStatus('error', 'WebRTC connection failed');
            throw error;
        }
    },

    playRemoteAudio(stream) {
        try {
            // Create audio element for remote stream
            const audioElement = document.createElement('audio');
            audioElement.srcObject = stream;
            audioElement.autoplay = true;
            audioElement.volume = 1.0;

            // Show wave animation when audio is playing
            audioElement.onplay = () => this.showWaveAnimation();
            audioElement.onended = () => this.hideWaveAnimation();

            // Add to DOM temporarily (required for some browsers)
            document.body.appendChild(audioElement);

            // Remove after a short delay
            setTimeout(() => {
                if (audioElement.parentNode) {
                    audioElement.parentNode.removeChild(audioElement);
                }
            }, 100);

        } catch (error) {
            console.error('Error playing remote audio:', error);
        }
    },

    showWaveAnimation() {
        const waveAnimation = document.getElementById('waveAnimation');
        if (waveAnimation) {
            waveAnimation.classList.add('active');

            // Hide wave animation after a delay (simulating speech duration)
            setTimeout(() => {
                this.hideWaveAnimation();
            }, 3000); // Adjust based on actual audio duration
        }
    },

    createAudioElement() {
        let audioOutput = document.getElementById('audio-output');
        if (!audioOutput) {
            audioOutput = document.createElement('audio');
            audioOutput.id = 'audio-output';
            audioOutput.autoplay = true;
            document.body.appendChild(audioOutput);
        }
        return audioOutput;
    },

    hideWaveAnimation() {
        const waveAnimation = document.getElementById('waveAnimation');
        if (waveAnimation) {
            waveAnimation.classList.remove('active');
        }
    },

    async stop() {
        try {
            // Stop recording
            if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
                this.mediaRecorder.stop();
            }

            // Close WebRTC peer connection
            if (this.peerConnection) {
                this.peerConnection.close();
                this.peerConnection = null;
            }

            // Stop audio stream
            if (AppState.localStream) {
                AppState.localStream.getTracks().forEach(track => track.stop());
                AppState.localStream = null;
            }

            // Close audio context
            if (this.audioContext) {
                this.audioContext.close();
                this.audioContext = null;
            }

            // Close WebSocket
            if (this.websocket) {
                this.websocket.send(JSON.stringify({
                    type: 'voice_end',
                    session_id: AppState.sessionId
                }));
                this.websocket.close();
                this.websocket = null;
            }

            // FastRTC handles session cleanup automatically

            AppState.isRecording = false;
            this.updateButtons(false);
            this.updateStatus('disconnected', 'Voice chat stopped');
            Utils.showNotification('Voice chat ended', 'info');

        } catch (error) {
            console.error('Error stopping voice chat:', error);
        }
    },

    updateButtons(isRecording) {
        const startBtn = document.getElementById('startVoiceBtn');
        const stopBtn = document.getElementById('stopVoiceBtn');

        if (startBtn) startBtn.disabled = isRecording;
        if (stopBtn) stopBtn.disabled = !isRecording;
    },

    updateStatus(status, message) {
        const statusElement = document.getElementById('voiceStatus');
        if (statusElement) {
            statusElement.className = `status-indicator ${status}`;
            const statusText = statusElement.querySelector('span:last-child');
            if (statusText) {
                statusText.textContent = message;
            }
        }
    }
};

// Video Chat functionality with real-time WebRTC - Based on working implementation
const VideoChat = {
    peerConnection: null,
    videoPeerConnection: null,
    localStream: null,
    localVideo: null,
    dataChannel: null,
    videoDataChannel: null,
    webrtc_id: null,
    video_webrtc_id: null,
    isCameraOn: true,
    isMuted: false,
    uploadedFiles: [],

    // RTC Configuration matching working implementation
    RTC_CONFIGURATION: {
        iceServers: [
            { urls: ["stun:fr-turn7.xirsys.com"] },
            { urls: ["stun:stun.l.google.com:19302"] },
            { urls: ["stun:stun1.l.google.com:19302"] },
            {
                username: "UIuBt8vNm5tNifOx-1ZY-nlw-mNMjhzc_2LsV1Wjpu2ccJ8-u_6wlgw0j7TxEvi6AAAAAGhSBQhNb29tYXJh",
                credential: "48a4de2c-4bd9-11f0-a9be-6aee953622e2",
                urls: [
                    "turn:fr-turn7.xirsys.com:80?transport=udp",
                    "turn:fr-turn7.xirsys.com:3478?transport=udp",
                    "turn:fr-turn7.xirsys.com:80?transport=tcp",
                    "turn:fr-turn7.xirsys.com:3478?transport=tcp",
                    "turns:fr-turn7.xirsys.com:443?transport=tcp",
                    "turns:fr-turn7.xirsys.com:5349?transport=tcp"
                ]
            }
        ]
    },

    init() {
        this.setupEventListeners();
        this.setupFileUpload();
        this.localVideo = document.getElementById('localVideo');
    },

    setupEventListeners() {
        const startBtn = document.getElementById('startVideoBtn');
        const stopBtn = document.getElementById('stopVideoBtn');
        const toggleCameraBtn = document.getElementById('toggleCameraBtn');
        const toggleMicBtn = document.getElementById('toggleMicBtn');
        const uploadFileBtn = document.getElementById('uploadFileBtn');

        if (startBtn) {
            startBtn.addEventListener('click', () => this.start());
        }

        if (stopBtn) {
            stopBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Stop button clicked');
                this.stop();
            });
        }

        if (toggleCameraBtn) {
            toggleCameraBtn.addEventListener('click', () => this.toggleCamera());
        }

        if (toggleMicBtn) {
            toggleMicBtn.addEventListener('click', () => this.toggleMic());
        }

        if (uploadFileBtn) {
            uploadFileBtn.addEventListener('click', () => this.toggleFileUpload());
        }
    },

    setupFileUpload() {
        const uploadZone = document.getElementById('uploadZone');
        const fileInput = document.getElementById('fileInput');
        const uploadedFilesContainer = document.getElementById('uploadedFiles');

        if (uploadZone && fileInput) {
            uploadZone.addEventListener('click', () => {
                fileInput.click();
            });

            uploadZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadZone.classList.add('dragover');
            });

            uploadZone.addEventListener('dragleave', () => {
                uploadZone.classList.remove('dragover');
            });

            uploadZone.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadZone.classList.remove('dragover');
                const files = e.dataTransfer.files;
                this.handleFiles(files);
            });

            fileInput.addEventListener('change', (e) => {
                const files = e.target.files;
                this.handleFiles(files);
            });
        }
    },

    async handleFiles(files) {
        for (let file of files) {
            try {
                const formData = new FormData();
                formData.append('file', file);

                const response = await fetch(`${CONFIG.apiBaseUrl}/upload_file`, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    this.uploadedFiles.push(result.encoded_data);
                    this.displayUploadedFile(result.filename, result.size);
                    Utils.showNotification(`File "${result.filename}" uploaded successfully!`, 'success');
                } else {
                    throw new Error('Upload failed');
                }
            } catch (error) {
                console.error('File upload error:', error);
                Utils.showNotification(`Failed to upload "${file.name}"`, 'error');
            }
        }
    },

    displayUploadedFile(filename, size) {
        const uploadedFilesContainer = document.getElementById('uploadedFiles');
        if (uploadedFilesContainer) {
            const fileDiv = document.createElement('div');
            fileDiv.className = 'uploaded-file';
            fileDiv.innerHTML = `
                <span>📄 ${filename} (${Math.round(size / 1024)}KB)</span>
                <button class="file-remove" onclick="VideoChat.removeFile(this, '${filename}')">×</button>
            `;
            uploadedFilesContainer.appendChild(fileDiv);
        }
    },

    removeFile(button, filename) {
        // Remove from uploaded files array
        this.uploadedFiles = this.uploadedFiles.filter(file => !file.data || file.filename !== filename);
        // Remove from UI
        button.parentElement.remove();
        Utils.showNotification(`Removed "${filename}"`, 'warning');
    },

    async checkCameraPermission() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ 
                video: true, 
                audio: true 
            });
            stream.getTracks().forEach(track => track.stop());
            return true;
        } catch (error) {
            console.warn('Camera/microphone permission not granted:', error);
            return false;
        }
    },

    async start() {
        try {
            if (!AppState.sessionId) {
                AppState.sessionId = Utils.generateSessionId();
            }

            this.updateStatus('connecting', 'Starting video chat...');

            // Generate unique WebRTC ID for video session
            this.video_webrtc_id = Math.random().toString(36).substring(7);

            // Set input parameters for FastRTC video stream
            const inputResponse = await Utils.apiCall('/input_hook', {
                method: 'POST',
                body: JSON.stringify({
                    webrtc_id: this.video_webrtc_id,
                    voice_name: 'Aoede',
                    mode: 'video',
                    uploaded_files: this.uploadedFiles
                })
            });

            if (inputResponse.status === 'error') {
                throw new Error(inputResponse.message || 'Failed to initialize video session');
            }

            // Get user media
            console.log('🎥 Requesting camera and microphone access...');
            this.localStream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    frameRate: { ideal: 15 }
                },
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true
                }
            });
            console.log('🎥 Camera and microphone access granted');

            // Display local video
            if (this.localVideo) {
                this.localVideo.srcObject = this.localStream;
            }

            AppState.localStream = this.localStream;

            // Initialize WebRTC peer connection with proper configuration
            this.videoPeerConnection = new RTCPeerConnection(this.RTC_CONFIGURATION);

            // Add local stream to peer connection
            this.localStream.getTracks().forEach(track => {
                this.videoPeerConnection.addTrack(track, this.localStream);
            });

            // Handle remote stream (audio from AI)
            this.videoPeerConnection.addEventListener('track', (evt) => {
                if (evt.track.kind === 'audio') {
                    const audioElement = new Audio();
                    audioElement.srcObject = evt.streams[0];
                    audioElement.play().catch(e => console.error("Video audio play failed:", e));
                }
            });

            // Handle ICE candidates
            this.videoPeerConnection.onicecandidate = ({ candidate }) => {
                if (candidate) {
                    console.debug("Sending ICE candidate", candidate);
                    fetch('/video/webrtc/offer', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            candidate: candidate.toJSON(),
                            webrtc_id: this.video_webrtc_id,
                            type: "ice-candidate",
                        })
                    });
                }
            };

            // Handle connection state changes
            this.videoPeerConnection.addEventListener('connectionstatechange', () => {
                console.log('Video connection state:', this.videoPeerConnection.connectionState);
                if (this.videoPeerConnection.connectionState === 'connected') {
                    Utils.showNotification('Connected to Mai Video!', 'success');
                    this.updateStatus('connected', 'Video session active - say "bye" to end');
                    AppState.isVideoActive = true;
                } else if (['disconnected', 'failed', 'closed'].includes(this.videoPeerConnection.connectionState)) {
                    this.updateStatus('disconnected', 'Video session ended');
                    AppState.isVideoActive = false;
                    // Don't auto-stop here to avoid recursion
                }
                this.updateVideoButtonState();
            });

            // Create data channel for control messages
            this.videoDataChannel = this.videoPeerConnection.createDataChannel('control');
            this.videoDataChannel.onmessage = (event) => {
                const eventJson = JSON.parse(event.data);
                if (eventJson.type === "error") {
                    Utils.showNotification(eventJson.message, 'error');
                } else if (eventJson.type === "send_input") {
                    fetch(`${CONFIG.apiBaseUrl}/input_hook`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            webrtc_id: this.video_webrtc_id,
                            voice_name: 'Aoede',
                            mode: "video",
                            uploaded_files: this.uploadedFiles
                        })
                    });
                }
            };

            // Create and send offer
            const offer = await this.videoPeerConnection.createOffer();
            await this.videoPeerConnection.setLocalDescription(offer);

            const response = await fetch('/video/webrtc/offer', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    sdp: this.videoPeerConnection.localDescription.sdp,
                    type: this.videoPeerConnection.localDescription.type,
                    webrtc_id: this.video_webrtc_id,
                })
            });

            const serverResponse = await response.json();

            if (serverResponse.status === 'failed') {
                const errorMsg = serverResponse.meta.error === 'concurrency_limit_reached'
                    ? `Too many active sessions. Maximum limit is ${serverResponse.meta.limit}. Please try again later.`
                    : `Connection failed: ${serverResponse.meta.error}`;
                Utils.showNotification(errorMsg, 'error');
                this.stop();
                return;
            }

            await this.videoPeerConnection.setRemoteDescription(serverResponse);

            this.updateStatus('connected', 'Video chat active');
            AppState.isVideoActive = true;

            // Update UI
            this.updateVideoButtonState();

        } catch (error) {
            console.error('Video chat error:', error);

            // Provide specific error messages for common issues
            let errorMessage = error.message;
            if (error.name === 'NotAllowedError') {
                errorMessage = 'Camera/microphone access denied. Please allow permissions and try again.';
            } else if (error.name === 'NotFoundError') {
                errorMessage = 'No camera or microphone found. Please check your devices.';
            } else if (error.name === 'NotReadableError') {
                errorMessage = 'Camera/microphone is already in use by another application.';
            }

            this.updateStatus('error', `Video error: ${errorMessage}`);
            Utils.showNotification(`Video chat failed: ${errorMessage}`, 'error');
            this.cleanup();
        }
    },

    toggleFileUpload() {
        const fileUploadPanel = document.getElementById('fileUploadPanel');
        const uploadFileBtn = document.getElementById('uploadFileBtn');

        if (fileUploadPanel) {
            const isVisible = fileUploadPanel.classList.contains('active');
            if (isVisible) {
                fileUploadPanel.classList.remove('active');
                fileUploadPanel.style.display = 'none';
                if (uploadFileBtn) uploadFileBtn.textContent = '📁 Upload Files';
            } else {
                fileUploadPanel.classList.add('active');
                fileUploadPanel.style.display = 'flex';
                if (uploadFileBtn) uploadFileBtn.textContent = '📁 Hide Files';
            }
        }
    },

    updateVideoButtonState() {
        const startBtn = document.getElementById('startVideoBtn');
        const stopBtn = document.getElementById('stopVideoBtn');
        const toggleCameraBtn = document.getElementById('toggleCameraBtn');
        const toggleMicBtn = document.getElementById('toggleMicBtn');
        const uploadFileBtn = document.getElementById('uploadFileBtn');

        console.log('Updating video button state. Connection state:',
            this.videoPeerConnection ? this.videoPeerConnection.connectionState : 'no connection');

        if (this.videoPeerConnection &&
            (this.videoPeerConnection.connectionState === 'connected' ||
             this.videoPeerConnection.connectionState === 'connecting')) {
            // Video session is active
            if (startBtn) {
                startBtn.style.display = 'none';
                startBtn.disabled = true;
            }
            if (stopBtn) {
                stopBtn.style.display = 'inline-block';
                stopBtn.disabled = false;
            }
            if (toggleCameraBtn) toggleCameraBtn.style.display = 'inline-block';
            if (toggleMicBtn) toggleMicBtn.style.display = 'inline-block';
            if (uploadFileBtn) uploadFileBtn.style.display = 'inline-block';
        } else {
            // Video session is not active
            if (startBtn) {
                startBtn.style.display = 'inline-block';
                startBtn.disabled = false;
            }
            if (stopBtn) {
                stopBtn.style.display = 'none';
                stopBtn.disabled = true;
            }
            if (toggleCameraBtn) toggleCameraBtn.style.display = 'none';
            if (toggleMicBtn) toggleMicBtn.style.display = 'none';
            if (uploadFileBtn) uploadFileBtn.style.display = 'none';

            // Hide file upload panel when session ends
            const fileUploadPanel = document.getElementById('fileUploadPanel');
            if (fileUploadPanel) {
                fileUploadPanel.classList.remove('active');
                fileUploadPanel.style.display = 'none';
            }
        }
    },

    toggleCamera() {
        if (this.localStream) {
            const videoTrack = this.localStream.getVideoTracks()[0];
            if (videoTrack) {
                this.isCameraOn = !this.isCameraOn;
                videoTrack.enabled = this.isCameraOn;

                const toggleCameraBtn = document.getElementById('toggleCameraBtn');
                if (toggleCameraBtn) {
                    toggleCameraBtn.textContent = this.isCameraOn ? '📷 Toggle Camera' : '📷 Camera Off';
                }

                Utils.showNotification(this.isCameraOn ? 'Camera turned on' : 'Camera turned off', 'info');
            }
        }
    },

    toggleMic() {
        if (this.localStream) {
            const audioTrack = this.localStream.getAudioTracks()[0];
            if (audioTrack) {
                this.isMuted = !this.isMuted;
                audioTrack.enabled = !this.isMuted;

                const toggleMicBtn = document.getElementById('toggleMicBtn');
                if (toggleMicBtn) {
                    toggleMicBtn.textContent = this.isMuted ? '🎤 Mic Off' : '🎤 Toggle Mic';
                }

                Utils.showNotification(this.isMuted ? 'Microphone muted' : 'Microphone unmuted', 'info');
            }
        }
    },

    async stop() {
        try {
            console.log('Stopping video chat...');
            this.updateStatus('disconnecting', 'Stopping video chat...');

            // Close data channel
            if (this.videoDataChannel) {
                this.videoDataChannel.close();
                this.videoDataChannel = null;
            }

            // Stop local stream
            if (this.localStream) {
                this.localStream.getTracks().forEach(track => {
                    track.stop();
                    console.log(`Stopped ${track.kind} track`);
                });
                this.localStream = null;
            }

            // Close peer connection
            if (this.videoPeerConnection) {
                this.videoPeerConnection.close();
                this.videoPeerConnection = null;
                console.log('Video peer connection closed');
            }

            // Clear video elements
            if (this.localVideo) {
                this.localVideo.srcObject = null;
            }

            // Remove remote audio element if present
            const audioOutput = document.getElementById('video-audio-output');
            if (audioOutput) {
                audioOutput.srcObject = null;
                audioOutput.remove();
            }

            // Reset state
            this.updateStatus('disconnected', 'Video chat stopped');
            AppState.isVideoActive = false;
            AppState.localStream = null;
            this.isCameraOn = true;
            this.isMuted = false;

            // Update UI
            this.updateVideoButtonState();

            console.log('Video chat stopped successfully');

        } catch (error) {
            console.error('Error stopping video chat:', error);
            // Force UI update even if there's an error
            this.updateVideoButtonState();
        }
    },

    cleanup() {
        this.stop();
    },

    updateStatus(status, message) {
        const statusElement = document.getElementById('videoStatus');
        if (statusElement) {
            statusElement.className = `status-indicator ${status}`;
            const statusText = statusElement.querySelector('span:last-child');
            if (statusText) {
                statusText.textContent = message;
            }
        }
    },

    async checkCameraPermission() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: true,
                audio: true
            });
            stream.getTracks().forEach(track => track.stop());
            return true;
        } catch (error) {
            console.warn('Camera/microphone permission check failed:', error);
            return false;
        }
    }
};

// Contact Form functionality
const ContactForm = {
    init() {
        this.setupEventListeners();
    },

    setupEventListeners() {
        const form = document.getElementById('contactFormElement');
        if (form) {
            form.addEventListener('submit', (e) => this.handleSubmit(e));
        }
    },

    async handleSubmit(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const contactInfo = {
            name: formData.get('name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            company: formData.get('company'),
            purpose: formData.get('purpose')
        };

        // Validate required fields
        if (!contactInfo.name || !contactInfo.email || !contactInfo.purpose) {
            Utils.showNotification('Please fill in all required fields', 'error');
            return;
        }

        // Show loading state
        const submitBtn = event.target.querySelector('.submit-btn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Sending...';
        submitBtn.disabled = true;

        try {
            console.log('📤 Submitting contact form:', contactInfo);
            const response = await Utils.apiCall('/contact', {
                method: 'POST',
                body: JSON.stringify(contactInfo)
            });

            console.log('📨 Contact form response:', response);

            if (response.status === 'success') {
                Utils.showNotification(
                    `Thank you! Your inquiry has been submitted. ${response.emails_sent} confirmation emails sent.`,
                    'success'
                );

                // Reset form
                event.target.reset();

                // Store contact info
                AppState.contactInfo = contactInfo;

            } else {
                Utils.showNotification('Failed to submit inquiry. Please try again.', 'error');
            }

        } catch (error) {
            console.error('Contact form submission error:', error);
            Utils.showNotification('Failed to submit inquiry. Please try again.', 'error');
        } finally {
            // Restore button
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }
};

// Application initialization
class MaiVoiceAgent {
    constructor() {
        this.initialized = false;
    }

    async init() {
        if (this.initialized) return;

        try {
            // Initialize mode manager
            ModeManager.init();

            // Initialize default mode (text chat)
            TextChat.init();

            // Setup global event listeners
            this.setupGlobalEventListeners();

            // Check API health
            await this.checkApiHealth();

            this.initialized = true;
            console.log('Mai Voice Agent initialized successfully');

        } catch (error) {
            console.error('Failed to initialize Mai Voice Agent:', error);
            Utils.showNotification('Failed to initialize application', 'error');
        }
    }

    setupGlobalEventListeners() {
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseActivities();
            } else {
                this.resumeActivities();
            }
        });

        // Handle beforeunload to clean up sessions
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });

        // Handle keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Enter to send message in chat mode
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter' && AppState.currentMode === 'chat') {
                TextChat.sendMessage();
            }
        });
    }

    async checkApiHealth() {
        try {
            const response = await Utils.apiCall('/health');
            if (response.status === 'healthy') {
                console.log('API health check passed');
            }
        } catch (error) {
            console.error('API health check failed:', error);
            Utils.showNotification('Warning: API connection issues detected', 'warning');
        }
    }

    pauseActivities() {
        if (AppState.currentMode === 'voice' && AppState.isRecording) {
            VoiceChat.stop();
        }

        if (AppState.currentMode === 'video' && AppState.isVideoActive) {
            VideoChat.stop();
        }
    }

    resumeActivities() {
        if (AppState.currentMode === 'chat' && AppState.websocket) {
            AppState.websocket.send(JSON.stringify({ type: 'ping' }));
        }
    }

    cleanup() {
        try {
            if (AppState.websocket) {
                TextChat.disconnect();
            }

            if (AppState.isRecording) {
                VoiceChat.stop();
            }

            if (AppState.isVideoActive) {
                VideoChat.stop();
            }
        } catch (error) {
            console.error('Cleanup error:', error);
        }
    }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    const app = new MaiVoiceAgent();
    await app.init();
});

// Export for global access
window.MaiVoiceAgent = {
    AppState,
    Utils,
    ModeManager,
    TextChat,
    VoiceChat,
    VideoChat,
    ContactForm
};




