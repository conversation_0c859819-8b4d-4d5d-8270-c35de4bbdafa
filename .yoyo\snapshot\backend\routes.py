"""
API routes for Mai Voice Agent
Defines all FastAPI endpoints and WebSocket handlers
"""

import logging
import uuid
import json
from typing import Dict, Any
from datetime import datetime

from fastapi import APIRouter, HTTPException, WebSocket, WebSocketDisconnect, Request, UploadFile, File
from fastapi.responses import JSONResponse

from config import settings, AVAILABLE_VOICES
from models import (
    ChatRequest, ChatResponse, VoiceRequest, VideoRequest,
    ContactInfo, SessionEndRequest, StatusResponse, SessionResponse,
    VoiceStatusResponse, HealthResponse, EmailResponse,
    session_manager
)
from ai_handlers import ai_handler
from email_service import email_service

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Health and Status Endpoints
@router.get("/")
async def root():
    """Root endpoint for basic connectivity"""
    return {"message": "Mai Voice Agent API", "status": "running", "version": "1.1"}

@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        active_sessions = len(session_manager.get_active_sessions())
        
        return HealthResponse(
            status="healthy",
            timestamp=datetime.utcnow(),
            service="mai-voice-agent",
            version="1.0.0",
            features={
                "text_chat": True,
                "voice_chat": True,
                "video_chat": True,
                "email_followup": True,
                "session_management": True
            },
            active_sessions=active_sessions
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Service unhealthy")

@router.get("/status", response_model=StatusResponse)
async def get_status():
    """Get service status"""
    return StatusResponse(
        status="operational",
        message="Mai Voice Agent is running normally",
        timestamp=datetime.utcnow()
    )

# Chat Endpoints
@router.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Text chat with Mai"""
    try:
        response = await ai_handler.generate_chat_response(request)
        return response
    except Exception as e:
        logger.error(f"Chat endpoint error: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate response")

# Voice Chat Endpoints
@router.get("/voice/status", response_model=VoiceStatusResponse)
async def voice_status():
    """Get voice chat status and available voices"""
    return VoiceStatusResponse(
        available=True,
        voice_name=settings.default_voice,
        webrtc_available=True,
        error=None
    )

@router.get("/voice/voices")
async def get_available_voices():
    """Get list of available voices"""
    return {"voices": AVAILABLE_VOICES}

@router.post("/voice/start", response_model=SessionResponse)
async def start_voice_chat(request: VoiceRequest):
    """Start voice chat session"""
    try:
        result = await ai_handler.start_voice_session(request)
        
        return SessionResponse(
            status=result["status"],
            session_id=result.get("session_id"),
            session_type="voice",
            message=result.get("message")
        )
    except Exception as e:
        logger.error(f"Voice start error: {e}")
        raise HTTPException(status_code=500, detail="Failed to start voice session")

# Video Chat Endpoints
@router.post("/video/start", response_model=SessionResponse)
async def start_video_chat(request: VideoRequest):
    """Start video chat session"""
    try:
        result = await ai_handler.start_video_session(request)
        
        return SessionResponse(
            status=result["status"],
            session_id=result.get("session_id"),
            session_type="video",
            message=result.get("message")
        )
    except Exception as e:
        logger.error(f"Video start error: {e}")
        raise HTTPException(status_code=500, detail="Failed to start video session")

# Session Management
@router.get("/session/{session_id}")
async def get_session_status(session_id: str):
    """Get session status"""
    try:
        status = ai_handler.get_session_status(session_id)
        return status
    except Exception as e:
        logger.error(f"Session status error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get session status")

@router.post("/session/end", response_model=SessionResponse)
async def end_session(request: SessionEndRequest):
    """End a session"""
    try:
        if not request.session_id:
            raise HTTPException(status_code=400, detail="Session ID required")
        
        result = await ai_handler.end_session(request.session_id)
        
        return SessionResponse(
            status=result["status"],
            session_id=request.session_id,
            message=result.get("message")
        )
    except Exception as e:
        logger.error(f"End session error: {e}")
        raise HTTPException(status_code=500, detail="Failed to end session")

# Contact Form Endpoint
@router.post("/contact", response_model=EmailResponse)
async def submit_contact_form(contact_info: ContactInfo):
    """Submit contact form and send follow-up emails"""
    try:
        # Create a simple conversation memory for the contact form
        from models import ConversationMemory, MessageType
        
        session_id = str(uuid.uuid4())
        memory = ConversationMemory(session_id=session_id)
        
        # Add contact form submission as a message
        form_message = f"Contact form submitted: {contact_info.purpose or 'General inquiry'}"
        memory.add_message(MessageType.SYSTEM, form_message)
        
        # Update contact info in memory
        memory.contact_info = contact_info
        
        # Send follow-up emails
        email_response = await email_service.send_follow_up_emails(contact_info, memory)
        
        logger.info(f"Contact form processed, {email_response.emails_sent} emails sent")
        
        return email_response
        
    except Exception as e:
        logger.error(f"Contact form error: {e}")
        raise HTTPException(status_code=500, detail="Failed to process contact form")

# Input hook for FastRTC streams
@router.post("/input_hook")
async def set_input_hook(body: dict):
    """Set input parameters for WebRTC stream with validation"""
    try:
        from webrtc_handler import audio_stream, video_stream

        webrtc_id = body.get("webrtc_id")
        voice_name = body.get("voice_name", "Aoede")
        mode = body.get("mode", "audio")
        uploaded_files = body.get("uploaded_files")

        if not webrtc_id:
            return {
                "status": "error",
                "message": "webrtc_id is required"
            }

        # Validate voice name
        valid_voices = ["Puck", "Charon", "Kore", "Fenrir", "Aoede"]
        if voice_name not in valid_voices:
            logger.warning(f"Invalid voice {voice_name}, using Aoede")
            voice_name = "Aoede"

        # Set input for appropriate stream
        if mode == "video":
            video_stream.set_input(webrtc_id, voice_name, uploaded_files)
        else:
            audio_stream.set_input(webrtc_id, voice_name)

        logger.info(f"Input set for WebRTC ID: {webrtc_id}, Voice: {voice_name}, Mode: {mode}")
        return {"status": "ok", "webrtc_id": webrtc_id, "voice": voice_name, "mode": mode}

    except Exception as e:
        logger.error(f"Error setting input hook: {e}")
        return {
            "status": "error",
            "message": f"Failed to set input parameters: {str(e)}"
        }

# File Upload Endpoint for Video Chat
@router.post("/upload_file")
async def upload_file(file: UploadFile = File(...)):
    """Upload file to share with AI during video chat"""
    try:
        import base64
        from io import BytesIO
        from PIL import Image

        # Read file content
        file_content = await file.read()

        # Determine mime type
        mime_type = file.content_type or "application/octet-stream"

        # For images, convert to JPEG if needed
        if mime_type.startswith("image/"):
            try:
                pil_image = Image.open(BytesIO(file_content))
                with BytesIO() as output_bytes:
                    pil_image.save(output_bytes, "JPEG")
                    file_content = output_bytes.getvalue()
                mime_type = "image/jpeg"
            except Exception as img_error:
                logger.warning(f"Could not process image: {img_error}")

        # Encode file for Gemini
        base64_str = base64.b64encode(file_content).decode("UTF-8")
        encoded_file = {"mime_type": mime_type, "data": base64_str}

        logger.info(f"File uploaded: {file.filename}, type: {mime_type}, size: {len(file_content)} bytes")

        return {
            "status": "success",
            "filename": file.filename,
            "mime_type": mime_type,
            "size": len(file_content),
            "encoded_data": encoded_file
        }

    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        raise HTTPException(status_code=500, detail=f"File upload failed: {str(e)}")

# FastRTC streams handle WebRTC endpoints automatically at /audio and /video paths

# WebSocket for Real-time Communication
@router.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for real-time chat"""
    await websocket.accept()
    logger.info(f"WebSocket connected for session {session_id}")
    
    try:
        # Get or create session
        memory = session_manager.get_session(session_id)
        if not memory:
            from models import SessionType
            memory = session_manager.create_session(session_id, SessionType.TEXT)
        
        while True:
            # Receive message from client
            data = await websocket.receive_json()
            logger.info(f"💬 Received WebSocket message: {data}")

            if data.get("type") == "chat":
                # Handle chat message
                user_message = data.get("message", "")
                
                if user_message.strip():
                    # Add user message to memory
                    memory.add_message("user", user_message)
                    
                    # Generate AI response
                    from models import ChatRequest
                    chat_request = ChatRequest(
                        prompt=user_message,
                        session_id=session_id
                    )
                    
                    response = await ai_handler.generate_chat_response(chat_request)
                    
                    # Send response back to client
                    await websocket.send_json({
                        "type": "chat_response",
                        "message": response.response,
                        "session_id": session_id,
                        "timestamp": response.timestamp.isoformat()
                    })
            
            elif data.get("type") == "end_session":
                # Handle session end
                result = await ai_handler.end_session(session_id)
                
                await websocket.send_json({
                    "type": "session_ended",
                    "status": result["status"],
                    "message": result.get("message"),
                    "emails_sent": result.get("emails_sent", 0)
                })
                break
            
            elif data.get("type") == "ping":
                # Handle ping for connection keep-alive
                await websocket.send_json({"type": "pong"})
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for session {session_id}")
    except Exception as e:
        logger.error(f"WebSocket error for session {session_id}: {e}")
        try:
            await websocket.send_json({
                "type": "error",
                "message": "Connection error occurred"
            })
        except:
            pass
    finally:
        # Clean up session if needed
        try:
            await ai_handler.end_session(session_id)
        except:
            pass

# WebSocket endpoint for real-time voice communication
@router.websocket("/voice/ws/{session_id}")
async def voice_websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for real-time voice communication"""
    await websocket.accept()
    logger.info(f"Voice WebSocket connected for session {session_id}")

    try:
        # Initialize voice session
        voice_session = ai_handler.active_voice_sessions.get(session_id)
        if not voice_session:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "Voice session not found. Please start a voice session first."
            }))
            await websocket.close()
            return

        # Send initial voice configuration
        await websocket.send_text(json.dumps({
            "type": "voice_ready",
            "session_id": session_id,
            "voice_name": voice_session.get("voice_name", "Aoede"),
            "message": "Voice session ready. Start speaking!"
        }))

        while True:
            # Receive audio data or control messages
            data = await websocket.receive_text()
            message_data = json.loads(data)

            if message_data.get("type") == "audio_data":
                # Process audio input and generate voice response
                audio_response = await ai_handler.process_voice_input(
                    session_id,
                    message_data.get("audio_data")
                )

                # Send voice response back
                await websocket.send_text(json.dumps({
                    "type": "voice_response",
                    "audio_data": audio_response.get("audio_data"),
                    "text": audio_response.get("text"),
                    "session_id": session_id
                }))

            elif message_data.get("type") == "voice_end":
                # End voice session
                await ai_handler.end_voice_session(session_id)
                await websocket.send_text(json.dumps({
                    "type": "voice_ended",
                    "message": "Voice session ended"
                }))
                break

    except WebSocketDisconnect:
        logger.info(f"Voice WebSocket disconnected for session {session_id}")
        await ai_handler.end_voice_session(session_id)
    except Exception as e:
        logger.error(f"Voice WebSocket error for session {session_id}: {e}")
        await websocket.close()

# WebSocket endpoint for real-time video communication
@router.websocket("/video/ws/{session_id}")
async def video_websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for real-time video communication"""
    await websocket.accept()
    logger.info(f"Video WebSocket connected for session {session_id}")

    try:
        # Initialize video session
        video_session = ai_handler.active_video_sessions.get(session_id)
        if not video_session:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "Video session not found. Please start a video session first."
            }))
            await websocket.close()
            return

        # Send initial video configuration
        await websocket.send_text(json.dumps({
            "type": "video_ready",
            "session_id": session_id,
            "voice_name": video_session.get("voice_name", "Aoede"),
            "message": "Video session ready. Camera and microphone active!"
        }))

        while True:
            # Receive video/audio data or control messages
            data = await websocket.receive_text()
            message_data = json.loads(data)

            if message_data.get("type") == "video_audio_data":
                # Process video and audio input
                response = await ai_handler.process_video_input(
                    session_id,
                    message_data.get("audio_data"),
                    message_data.get("video_data")
                )

                # Send video/audio response back
                await websocket.send_text(json.dumps({
                    "type": "video_response",
                    "audio_data": response.get("audio_data"),
                    "text": response.get("text"),
                    "session_id": session_id
                }))

            elif message_data.get("type") == "video_end":
                # End video session
                await ai_handler.end_video_session(session_id)
                await websocket.send_text(json.dumps({
                    "type": "video_ended",
                    "message": "Video session ended"
                }))
                break

    except WebSocketDisconnect:
        logger.info(f"Video WebSocket disconnected for session {session_id}")
        await ai_handler.end_video_session(session_id)
    except Exception as e:
        logger.error(f"Video WebSocket error for session {session_id}: {e}")
        await websocket.close()

# Video WebRTC endpoints
@router.post("/video/webrtc/offer")
async def video_webrtc_offer(request: Request):
    """Handle WebRTC offer for video chat"""
    try:
        data = await request.json()
        offer = data.get("offer")
        session_id = data.get("session_id")
        
        if not offer or not session_id:
            return {"error": "Missing offer or session_id"}
        
        # Process offer through video stream
        from webrtc_handler import video_stream
        
        # This would be handled by FastRTC internally
        # For now, return a mock answer
        answer = {
            "type": "answer",
            "sdp": "mock_answer_sdp"  # FastRTC will handle the actual SDP
        }
        
        logger.info(f"Video WebRTC offer processed for session: {session_id}")
        return {"answer": answer}
        
    except Exception as e:
        logger.error(f"Error processing video WebRTC offer: {e}")
        return {"error": str(e)}

@router.post("/video/webrtc/ice")
async def video_webrtc_ice(request: Request):
    """Handle ICE candidates for video chat"""
    try:
        data = await request.json()
        candidate = data.get("candidate")
        session_id = data.get("session_id")
        
        logger.info(f"ICE candidate received for video session: {session_id}")
        return {"status": "ok"}
        
    except Exception as e:
        logger.error(f"Error processing ICE candidate: {e}")
        return {"error": str(e)}

@router.post("/video/webrtc/close")
async def video_webrtc_close(request: Request):
    """Handle video chat session close"""
    try:
        data = await request.json()
        session_id = data.get("session_id")
        
        logger.info(f"Video session closed: {session_id}")
        return {"status": "closed"}
        
    except Exception as e:
        logger.error(f"Error closing video session: {e}")
        return {"error": str(e)}

# Admin Endpoints (for monitoring)
@router.get("/admin/sessions")
async def get_active_sessions():
    """Get list of active sessions (admin only)"""
    try:
        sessions = session_manager.get_active_sessions()
        session_details = []
        
        for session_id in sessions:
            status = ai_handler.get_session_status(session_id)
            session_details.append(status)
        
        return {
            "active_sessions": len(sessions),
            "sessions": session_details
        }
    except Exception as e:
        logger.error(f"Admin sessions error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get sessions")

@router.post("/admin/cleanup")
async def cleanup_sessions():
    """Clean up inactive sessions (admin only)"""
    try:
        ai_handler.cleanup_inactive_sessions()
        return {"status": "success", "message": "Session cleanup completed"}
    except Exception as e:
        logger.error(f"Admin cleanup error: {e}")
        raise HTTPException(status_code=500, detail="Failed to cleanup sessions")

# Note: Exception handlers are defined in main.py for the FastAPI app
# APIRouter doesn't support exception handlers directly




